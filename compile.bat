@echo off
echo Compiling SincResampler with GCC...

gcc -Wall -Wextra -O2 -std=c99 -I. -c sincResampler.c -o sincResampler.o
if %errorlevel% neq 0 (
    echo Error compiling sincResampler.c
    pause
    exit /b 1
)

gcc -Wall -Wextra -O2 -std=c99 -I. -c wavhelper/wavhelper.c -o wavhelper.o
if %errorlevel% neq 0 (
    echo Error compiling wavhelper.c
    pause
    exit /b 1
)

gcc -Wall -Wextra -O2 -std=c99 -I. -c testResampler.c -o testResampler.o
if %errorlevel% neq 0 (
    echo Error compiling testResampler.c
    pause
    exit /b 1
)

gcc -o testResampler.exe sincResampler.o wavhelper.o testResampler.o -lm
if %errorlevel% neq 0 (
    echo Error linking testResampler
    pause
    exit /b 1
)

gcc -o simpleTest.exe sincResampler.o simpleTest.o -lm
if %errorlevel% neq 0 (
    echo Error linking simpleTest
    pause
    exit /b 1
)

gcc -o verify.exe sincResampler.o verify.o -lm
if %errorlevel% neq 0 (
    echo Error linking verify
    pause
    exit /b 1
)

echo Build successful!

echo Created: testResampler.exe (WAV file processor)
echo To run WAV tests: testResampler.exe
pause
