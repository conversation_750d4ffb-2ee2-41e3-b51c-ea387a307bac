﻿#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include "wavhelper.h"

// 只支持RIFF PCM/WAVE格式
WavFile* wavReadToFloat(const char* filename) {
    FILE* f = fopen(filename, "rb");
    if (!f) return NULL;
    // 读取RIFF头
    char riff[4]; fread(riff, 1, 4, f);
    if (memcmp(riff, "RIFF", 4) != 0) { fclose(f); return NULL; }
    fseek(f, 4, SEEK_CUR); // 跳过文件大小
    char wave[4]; fread(wave, 1, 4, f);
    if (memcmp(wave, "WAVE", 4) != 0) { fclose(f); return NULL; }
    // 查找fmt/data块
    int fmtFound = 0, dataFound = 0;
    int16_t audioFormat = 0, numChannels = 0, bitsPerSample = 0;
    int sampleRate = 0;
    int64_t dataSize = 0, dataPos = 0;
    while (!fmtFound || !dataFound) {
        char chunkId[4];
        if (fread(chunkId, 1, 4, f) != 4) break;
        uint32_t chunkSize; fread(&chunkSize, 4, 1, f);
        if (memcmp(chunkId, "fmt ", 4) == 0) {
            fmtFound = 1;
            fread(&audioFormat, 2, 1, f);
            fread(&numChannels, 2, 1, f);
            fread(&sampleRate, 4, 1, f);
            fseek(f, 6, SEEK_CUR); // 跳过byteRate, blockAlign
            fread(&bitsPerSample, 2, 1, f);
            fseek(f, chunkSize - 16, SEEK_CUR);
        } else if (memcmp(chunkId, "data", 4) == 0) {
            dataFound = 1;
            dataSize = chunkSize;
            dataPos = ftell(f);
            fseek(f, chunkSize, SEEK_CUR);
        } else {
            fseek(f, chunkSize, SEEK_CUR);
        }
    }
    if (!fmtFound || !dataFound) { fclose(f); return NULL; }
    // 读取数据
    fseek(f, dataPos, SEEK_SET);
    int64_t numSamples = dataSize / (bitsPerSample/8);
    float* data = (float*)malloc(numSamples * sizeof(float));
    if (audioFormat == 1) { // PCM
        if (bitsPerSample == 16) {
            int16_t* buf = (int16_t*)malloc(numSamples*2);
            fread(buf, 2, numSamples, f);
            for (int64_t i = 0; i < numSamples; ++i)
                data[i] = buf[i] / 32768.0f;
            free(buf);
        } else if (bitsPerSample == 24) {
            for (int64_t i = 0; i < numSamples; ++i) {
                uint8_t b[3];
                fread(b, 1, 3, f);
                int32_t v = (b[2]<<24)|(b[1]<<16)|(b[0]<<8);
                v >>= 8; // sign extend
                data[i] = v / 8388608.0f;
            }
        } else if (bitsPerSample == 32) {
            int32_t* buf = (int32_t*)malloc(numSamples*4);
            fread(buf, 4, numSamples, f);
            for (int64_t i = 0; i < numSamples; ++i)
                data[i] = buf[i] / 2147483648.0f;
            free(buf);
        }
    } else if (audioFormat == 3 && bitsPerSample == 32) { // IEEE float
        fread(data, 4, numSamples, f);
    }
    fclose(f);
    WavFile* wav = (WavFile*)calloc(1, sizeof(WavFile));
    wav->sampleRate = sampleRate;
    wav->numChannels = numChannels;
    wav->bitsPerSample = bitsPerSample;
    wav->format = WAV_FLOAT;
    wav->numFrames = numSamples / numChannels;
    wav->data = data;
    return wav;
}

int wavWriteFromFloat(const char* filename, const float* data, int sampleRate, int numChannels, int frames, WavSampleFormat format) {
    FILE* f = fopen(filename, "wb");
    if (!f) return 0;
    int bitsPerSample = (format==WAV_INT16)?16:(format==WAV_INT24)?24:(format==WAV_INT32)?32:32;
    int byteRate = sampleRate * numChannels * bitsPerSample/8;
    int blockAlign = numChannels * bitsPerSample/8;
    int dataSize = frames * numChannels * bitsPerSample/8;
    int fmtSize = 16;
    int audioFormat = (format==WAV_FLOAT)?3:1;
    // RIFF头
    fwrite("RIFF", 1, 4, f);
    uint32_t riffSize = 36 + dataSize;
    fwrite(&riffSize, 4, 1, f);
    fwrite("WAVE", 1, 4, f);
    // fmt块
    fwrite("fmt ", 1, 4, f);
    fwrite(&fmtSize, 4, 1, f);
    fwrite(&audioFormat, 2, 1, f);
    fwrite(&numChannels, 2, 1, f);
    fwrite(&sampleRate, 4, 1, f);
    fwrite(&byteRate, 4, 1, f);
    fwrite(&blockAlign, 2, 1, f);
    fwrite(&bitsPerSample, 2, 1, f);
    // data块
    fwrite("data", 1, 4, f);
    fwrite(&dataSize, 4, 1, f);
    if (format == WAV_INT16) {
        for (int i = 0; i < frames*numChannels; ++i) {
            int16_t v = (int16_t)(data[i]*32767.0f);
            fwrite(&v, 2, 1, f);
        }
    } else if (format == WAV_INT24) {
        for (int i = 0; i < frames*numChannels; ++i) {
            int32_t v = (int32_t)(data[i]*8388607.0f);
            uint8_t b[3] = {v&0xFF, (v>>8)&0xFF, (v>>16)&0xFF};
            fwrite(b, 1, 3, f);
        }
    } else if (format == WAV_INT32) {
        for (int i = 0; i < frames*numChannels; ++i) {
            int32_t v = (int32_t)(data[i]*2147483647.0f);
            fwrite(&v, 4, 1, f);
        }
    } else if (format == WAV_FLOAT) {
        fwrite(data, 4, frames*numChannels, f);
    }
    fclose(f);
    return 1;
}

void wavFree(WavFile* wav) {
    if (!wav) return;
    if (wav->data) free(wav->data);
    free(wav);
}