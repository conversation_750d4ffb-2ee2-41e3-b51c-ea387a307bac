#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "sincResampler.h"
#include "wavhelper/wavhelper.h"

#ifdef _WIN32
#include <direct.h>
#include <io.h>
#define mkdir(path, mode) _mkdir(path)
#define access _access
#define F_OK 0
#else
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#endif

// Test configuration structure
typedef struct {
    const char* inputFile;
    int targetSampleRate;
    const char* outputFile;
} TestConfig;

// Create output directory if it doesn't exist
static int createOutputDir(const char* dirPath) {
#ifdef _WIN32
    if (_access(dirPath, F_OK) == -1) {
        return _mkdir(dirPath);
    }
#else
    struct stat st = {0};
    if (stat(dirPath, &st) == -1) {
        return mkdir(dirPath, 0755);
    }
#endif
    return 0;
}

// Process a single WAV file with resampling
static int processWavFile(const char* inputPath, const char* outputPath, int targetSampleRate) {
    printf("Processing: %s -> %s (target rate: %d Hz)\n", inputPath, outputPath, targetSampleRate);

    // Read input WAV file
    WavFile* inputWav = wavReadToFloat(inputPath);
    if (!inputWav) {
        printf("Error: Failed to read input file: %s\n", inputPath);
        return 0;
    }

    printf("Input: %d Hz, %d channels, %lld frames\n",
           inputWav->sampleRate, inputWav->numChannels, (long long)inputWav->numFrames);

    // Check if resampling is needed
    if (inputWav->sampleRate == targetSampleRate) {
        printf("No resampling needed, copying file...\n");
        int result = wavWriteFromFloat(outputPath, (float*)inputWav->data,
                                      inputWav->sampleRate, inputWav->numChannels,
                                      (int)inputWav->numFrames, WAV_INT16);
        wavFree(inputWav);
        return result;
    }

    // Calculate output parameters - ensure same duration
    double ratio = (double)targetSampleRate / inputWav->sampleRate;
    int64_t expectedOutputFrames = (int64_t)(inputWav->numFrames * ratio);

    printf("Resampling ratio: %.6f, expected output frames: %lld\n", ratio, (long long)expectedOutputFrames);

    // Allocate output buffer - use expected size
    float* outputData = (float*)malloc(expectedOutputFrames * inputWav->numChannels * sizeof(float));
    if (!outputData) {
        printf("Error: Failed to allocate output buffer\n");
        wavFree(inputWav);
        return 0;
    }
    
    // Process each channel separately
    int64_t actualOutputFrames = 0;

    for (int ch = 0; ch < inputWav->numChannels; ch++) {
        printf("Processing channel %d/%d...\n", ch + 1, inputWav->numChannels);

        // Extract channel data
        float* inputChannel = (float*)malloc(inputWav->numFrames * sizeof(float));
        float* outputChannel = (float*)malloc(expectedOutputFrames * sizeof(float));

        if (!inputChannel || !outputChannel) {
            printf("Error: Failed to allocate channel buffers\n");
            free(inputChannel);
            free(outputChannel);
            free(outputData);
            wavFree(inputWav);
            return 0;
        }

        // Extract input channel
        float* inputPtr = (float*)inputWav->data;
        for (int64_t i = 0; i < inputWav->numFrames; i++) {
            inputChannel[i] = inputPtr[i * inputWav->numChannels + ch];
        }

        // Create resampler for this channel - use fixed chunk size
        size_t chunkSize = 512;
        size_t outputChunkSize = (size_t)(chunkSize * ratio + 0.5);
        if (outputChunkSize == 0) outputChunkSize = 1;

        PushSincResampler* resampler = pushSincResamplerCreate(chunkSize, outputChunkSize);
        if (!resampler) {
            printf("Error: Failed to create resampler\n");
            free(inputChannel);
            free(outputChannel);
            free(outputData);
            wavFree(inputWav);
            return 0;
        }

        // Process in chunks of fixed size
        int64_t inputPos = 0;
        int64_t outputPos = 0;

        while (inputPos < inputWav->numFrames && outputPos < expectedOutputFrames) {
            size_t inputChunkSize = chunkSize;
            if ((int64_t)(inputPos + inputChunkSize) > inputWav->numFrames) {
                inputChunkSize = (size_t)(inputWav->numFrames - inputPos);

                // Pad the last chunk with zeros to maintain chunk size
                float* paddedChunk = (float*)calloc(chunkSize, sizeof(float));
                if (!paddedChunk) {
                    printf("Error: Failed to allocate padded chunk\n");
                    pushSincResamplerDestroy(resampler);
                    free(inputChannel);
                    free(outputChannel);
                    free(outputData);
                    wavFree(inputWav);
                    return 0;
                }

                // Copy actual data
                memcpy(paddedChunk, inputChannel + inputPos, inputChunkSize * sizeof(float));

                // Calculate remaining output space
                size_t remainingOutput = expectedOutputFrames - outputPos;

                // Resample padded chunk
                size_t actualOutput = pushSincResamplerResampleFloat(
                    resampler,
                    paddedChunk,
                    chunkSize,
                    outputChannel + outputPos,
                    remainingOutput
                );

                free(paddedChunk);
                inputPos += inputChunkSize;
                outputPos += actualOutput;
                break;
            }

            // Calculate remaining output space
            size_t remainingOutput = expectedOutputFrames - outputPos;

            // Resample full chunk
            size_t actualOutput = pushSincResamplerResampleFloat(
                resampler,
                inputChannel + inputPos,
                chunkSize,
                outputChannel + outputPos,
                remainingOutput
            );

            inputPos += chunkSize;
            outputPos += actualOutput;
        }

        // Flush remaining data from the resampler, but limit to expected output
        if (outputPos < expectedOutputFrames) {
            printf("Flushing remaining data for channel %d...\n", ch + 1);
            size_t remainingSpace = expectedOutputFrames - outputPos;
            size_t flushedSamples = pushSincResamplerFlush(
                resampler,
                outputChannel + outputPos,
                remainingSpace
            );
            outputPos += flushedSamples;
        }

        // Ensure we don't exceed expected output frames
        if (outputPos > expectedOutputFrames) {
            outputPos = expectedOutputFrames;
        }

        printf("Channel %d: processed %lld input samples -> %lld output samples (expected %lld)\n",
               ch + 1, (long long)inputPos, (long long)outputPos, (long long)expectedOutputFrames);

        // Store the actual output frame count from the first channel
        if (ch == 0) {
            actualOutputFrames = outputPos;
        }

        // Interleave output channel
        for (int64_t i = 0; i < outputPos; i++) {
            outputData[i * inputWav->numChannels + ch] = outputChannel[i];
        }

        pushSincResamplerDestroy(resampler);
        free(inputChannel);
        free(outputChannel);
    }
    
    // Write output file using actual output frame count
    printf("Writing output file with %lld frames...\n", (long long)actualOutputFrames);
    int result = wavWriteFromFloat(outputPath, outputData, targetSampleRate,
                                  inputWav->numChannels, (int)actualOutputFrames, WAV_INT16);

    if (result) {
        printf("Successfully wrote: %s (%lld frames)\n", outputPath, (long long)actualOutputFrames);
    } else {
        printf("Error: Failed to write output file: %s\n", outputPath);
    }
    
    free(outputData);
    wavFree(inputWav);
    return result;
}

int main() {
    printf("SincResampler Test Program\n");
    printf("==========================\n\n");
    
    // Create output directory
    if (createOutputDir("processedwav") != 0) {
        printf("Warning: Could not create processedwav directory\n");
    }
    
    // Test configurations - resample all files to different target rates
    TestConfig tests[] = {
        {"testwav/20_20k_16000_1ch_int16.wav", 48000, "processedwav/16k_to_48k.wav"},
        {"testwav/20_20k_22050_1ch_int16.wav", 44100, "processedwav/22k_to_44k.wav"},
        {"testwav/20_20k_32000_1ch_int16.wav", 48000, "processedwav/32k_to_48k.wav"},
        {"testwav/20_20k_44100_1ch_int16.wav", 16000, "processedwav/44k_to_16k.wav"},
        {"testwav/20_20k_48000_1ch_int16.wav", 22050, "processedwav/48k_to_22k.wav"},
    };
    
    int numTests = sizeof(tests) / sizeof(tests[0]);
    int successCount = 0;
    
    for (int i = 0; i < numTests; i++) {
        printf("Test %d/%d:\n", i + 1, numTests);
        
        if (processWavFile(tests[i].inputFile, tests[i].outputFile, tests[i].targetSampleRate)) {
            successCount++;
            printf("✓ Success\n\n");
        } else {
            printf("✗ Failed\n\n");
        }
    }
    
    printf("Results: %d/%d tests passed\n", successCount, numTests);
    
    if (successCount == numTests) {
        printf("All tests completed successfully!\n");
        return 0;
    } else {
        printf("Some tests failed.\n");
        return 1;
    }
}
