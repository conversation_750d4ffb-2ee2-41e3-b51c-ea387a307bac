#ifndef SINC_RESAMPLER_H
#define SINC_RESAMPLER_H

#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Constants
#define KERNEL_SIZE 32
#define DEFAULT_REQUEST_SIZE 512
#define KERNEL_OFFSET_COUNT 32
#define KERNEL_STORAGE_SIZE (KERNEL_SIZE * (KERNEL_OFFSET_COUNT + 1))

// Forward declarations
typedef struct SincResampler SincResampler;
typedef struct PushSincResampler PushSincResampler;

// Callback function type for providing input data
typedef void (*SincResamplerCallback)(void* userData, size_t frames, float* destination);

// SincResampler structure
typedef struct SincResampler {
    double ioSampleRateRatio;
    double virtualSourceIdx;
    int bufferPrimed;
    SincResamplerCallback readCallback;
    void* callbackUserData;
    size_t requestFrames;
    size_t blockSize;
    size_t inputBufferSize;
    
    // Aligned memory buffers
    float* kernelStorage;
    float* kernelPreSincStorage;
    float* kernelWindowStorage;
    float* inputBuffer;
    
    // Region pointers
    float* r0;
    float* r1;
    float* r2;
    float* r3;
    float* r4;
} SincResampler;

// PushSincResampler structure
typedef struct PushSincResampler {
    SincResampler* resampler;
    float* floatBuffer;
    const float* sourcePtr;
    const int16_t* sourcePtrInt;
    size_t destinationFrames;
    int firstPass;
    size_t sourceAvailable;
} PushSincResampler;

// SincResampler functions
SincResampler* sincResamplerCreate(double ioSampleRateRatio, 
                                   size_t requestFrames,
                                   SincResamplerCallback readCallback,
                                   void* callbackUserData);
void sincResamplerDestroy(SincResampler* resampler);
void sincResamplerResample(SincResampler* resampler, size_t frames, float* destination);
size_t sincResamplerChunkSize(const SincResampler* resampler);
void sincResamplerFlush(SincResampler* resampler);
void sincResamplerSetRatio(SincResampler* resampler, double ioSampleRateRatio);
float sincResamplerAlgorithmicDelaySeconds(int sourceRateHz);

// PushSincResampler functions
PushSincResampler* pushSincResamplerCreate(size_t sourceFrames, size_t destinationFrames);
void pushSincResamplerDestroy(PushSincResampler* resampler);
size_t pushSincResamplerResampleInt16(PushSincResampler* resampler,
                                      const int16_t* source,
                                      size_t sourceFrames,
                                      int16_t* destination,
                                      size_t destinationCapacity);
size_t pushSincResamplerResampleFloat(PushSincResampler* resampler,
                                      const float* source,
                                      size_t sourceFrames,
                                      float* destination,
                                      size_t destinationCapacity);

// Flush remaining data from the resampler (call at end of stream)
size_t pushSincResamplerFlush(PushSincResampler* resampler,
                              float* destination,
                              size_t destinationCapacity);

// Utility functions
void floatS16ToS16(const float* src, size_t length, int16_t* dest);
void s16ToFloatS16(const int16_t* src, size_t length, float* dest);

#ifdef __cplusplus
}
#endif

#endif // SINC_RESAMPLER_H
