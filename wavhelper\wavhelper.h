﻿// wavhelper.h
#ifndef WAVHELPER_H
#define WAVHELPER_H

#include <stdint.h>
#include <stdio.h>

/* 移除对外部不可用头文件的依赖，改为在此定义采样格式枚举 */
typedef enum {
    WAV_INT16 = 0,
    WAV_INT24,
    WAV_INT32,
    WAV_FLOAT
} WavSampleFormat;

typedef struct {
    int sampleRate;
    int numChannels;
    int bitsPerSample;
    WavSampleFormat format;
    int64_t numFrames;
    void* data; // 指向样本数据，float/int16/int32
} WavFile;

// 读取wav文件，自动转换为float格式，返回WavFile结构体
WavFile* wavReadToFloat(const char* filename);

// 写入wav文件，支持float/int16/int24/int32
int wavWriteFromFloat(const char* filename, const float* data, int sampleRate, int numChannels, int frames, WavSampleFormat format);

// 释放WavFile
void wavFree(WavFile* wav);

#endif